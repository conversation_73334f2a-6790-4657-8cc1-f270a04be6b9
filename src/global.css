@import 'tailwindcss';

/* Hero Section Pagination Styles */
.hero-swiper .swiper-pagination {
  position: absolute !important;
  z-index: 20 !important;
}

/* Desktop - Right side vertical */
@media (min-width: 768px) {
  .hero-swiper .swiper-pagination {
    right: 2rem !important;
    top: 50% !important;
    bottom: auto !important;
    left: auto !important;
    transform: translateY(-50%) !important;
    width: auto !important;
    height: auto !important;
    display: flex !important;
    flex-direction: column !important;
    gap: 1rem !important;
  }
}

/* Mobile - Bottom center horizontal */
@media (max-width: 767px) {
  .hero-swiper .swiper-pagination {
    bottom: 8rem !important;
    left: 50% !important;
    top: auto !important;
    right: auto !important;
    transform: translateX(-50%) !important;
    width: auto !important;
    height: auto !important;
    display: flex !important;
    flex-direction: row !important;
    gap: 0.75rem !important;
  }
}

.hero-swiper .swiper-pagination-bullet {
  width: 12px !important;
  height: 12px !important;
  background: rgba(255, 255, 255, 0.5) !important;
  opacity: 1 !important;
  border-radius: 50% !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  margin: 0 !important;
}

.hero-swiper .swiper-pagination-bullet-active {
  background: #00E0C6 !important;
  transform: scale(1.2) !important;
}

/* Mobile specific bullet size */
@media (max-width: 767px) {
  .hero-swiper .swiper-pagination-bullet {
    width: 10px !important;
    height: 10px !important;
  }
}
