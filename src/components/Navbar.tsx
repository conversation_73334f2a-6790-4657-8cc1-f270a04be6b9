const Navbar = () => (
  <nav className='w-full bg-[#011589] sticky top-0 z-20 shadow-[0_4px_10px_0_rgba(0,0,0,0.2)]'>
    <div className='max-w-screen-xl mx-auto flex items-center justify-between py-3 px-4 md:px-6'>
      {/* Left: Hamburger + Navigation Items */}
      <div className='flex items-center gap-4'>
        {/* Hamburger Menu */}
        <button className='text-white hover:opacity-80 transition-opacity'>
          <svg width='24' height='24' fill='none' viewBox='0 0 24 24' className='md:w-6 md:h-6'>
            <path
              stroke='currentColor'
              strokeWidth='2'
              strokeLinecap='round'
              d='M4 6h16M4 12h16M4 18h16'
            />
          </svg>
        </button>

        {/* Desktop Navigation Items */}
        <div className='hidden md:flex items-center gap-6'>
          {/* KLIA Terminal 1 */}
          <button className='text-white font-medium hover:opacity-80 transition-opacity'>
            KLIA TERMINAL 1
          </button>

          {/* KLIA Terminal 2 with Dropdown */}
          <div className='relative'>
            <button className='flex items-center gap-2 text-white font-medium hover:opacity-80 transition-opacity'>
              KLIA TERMINAL 2
              <svg width='12' height='12' fill='none' viewBox='0 0 24 24'>
                <path
                  stroke='currentColor'
                  strokeWidth='2'
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  d='M6 9l6 6 6-6'
                />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Center: Logo */}
      <div className='flex items-center gap-2'>
        {/* Logo Icon */}
        <div className='w-8 h-8 md:w-10 md:h-10'>
          <svg viewBox='0 0 40 40' fill='none' className='w-full h-full'>
            <rect width='40' height='40' rx='8' fill='#00E0C6'/>
            <path d='M12 10L28 10C30.2091 10 32 11.7909 32 14L32 26C32 28.2091 30.2091 30 28 30L12 30C9.79086 30 8 28.2091 8 26L8 14C8 11.7909 9.79086 10 12 10Z' fill='#011589'/>
            <path d='M16 14L24 14C25.1046 14 26 14.8954 26 16L26 24C26 25.1046 25.1046 26 24 26L16 26C14.8954 26 14 25.1046 14 24L14 16C14 14.8954 14.8954 14 16 14Z' fill='#00E0C6'/>
          </svg>
        </div>

        {/* Logo Text */}
        <span className='text-white font-bold text-lg md:text-xl'>
          CapsuleTransit
        </span>
      </div>

      {/* Right: CTA Button */}
      <div className='flex items-center'>
        {/* Desktop Button */}
        <button className='hidden md:block bg-[#00E0C6] hover:bg-[#00bfae] text-[#011589] font-bold px-6 py-2.5 rounded-lg shadow-lg transition-colors duration-200'>
          BOOK YOUR STAY
        </button>

        {/* Mobile Button */}
        <button className='md:hidden bg-[#00E0C6] hover:bg-[#00bfae] text-[#011589] font-bold px-6 py-2.5 rounded-lg shadow-lg transition-colors duration-200'>
          BOOK
        </button>
      </div>
    </div>
  </nav>
);

export default Navbar;