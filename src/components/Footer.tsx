import React from 'react';

const Footer = () => (
  <footer className='w-full bg-[#011589] text-white pt-16 pb-6 px-4 mt-12'>
    {/* Main Content Section */}
    <div className='max-w-7xl mx-auto'>
      {/* Mobile Layout */}
      <div className='block md:hidden'>
        <div className='grid grid-cols-1 gap-8 pb-10 border-b border-[#3a4bb3]'>
          {/* Terminal 1 */}
          <div>
            <div className='text-sm font-bold text-gray-300 mb-4 tracking-wide'>
              STAY AT KLIA TERMINAL 1
            </div>
            <ul className='space-y-3'>
              <li>
                <a href='#' className='text-lg font-semibold hover:underline'>
                  Sleep Lounge
                </a>
              </li>
            </ul>
          </div>

          {/* Terminal 2 */}
          <div>
            <div className='text-sm font-bold text-gray-300 mb-4 tracking-wide'>
              STAY AT KLIA TERMINAL 2
            </div>
            <ul className='space-y-3'>
              <li>
                <a href='#' className='text-lg font-semibold hover:underline'>
                  MAX
                </a>
              </li>
              <li>
                <a href='#' className='text-lg font-semibold hover:underline'>
                  Landside
                </a>
              </li>
              <li>
                <a href='#' className='text-lg font-semibold hover:underline'>
                  Airside
                </a>
              </li>
            </ul>
          </div>

          {/* Discover */}
          <div>
            <div className='text-sm font-bold text-gray-300 mb-4 tracking-wide'>
              DISCOVER
            </div>
            <ul className='space-y-3'>
              <li>
                <a href='#' className='text-lg hover:underline'>
                  About Us
                </a>
              </li>
              <li>
                <a href='#' className='text-lg hover:underline'>
                  Capsule Highlight
                </a>
              </li>
              <li>
                <a href='#' className='text-lg hover:underline'>
                  Our Commitment
                </a>
              </li>
              <li>
                <a href='#' className='text-lg hover:underline'>
                  Newsroom
                </a>
              </li>
              <li>
                <a href='#' className='text-lg hover:underline'>
                  FAQ
                </a>
              </li>
              <li>
                <a href='#' className='text-lg hover:underline'>
                  CSR
                </a>
              </li>
            </ul>
          </div>

          {/* Reach Us */}
          <div>
            <div className='text-sm font-bold text-gray-300 mb-4 tracking-wide'>
              REACH US
            </div>
            <ul className='space-y-3 mb-6'>
              <li>
                <a href='#' className='text-lg hover:underline'>
                  Contact
                </a>
              </li>
              <li>
                <a href='#' className='text-lg hover:underline'>
                  Career
                </a>
              </li>
            </ul>
            {/* Social Icons */}
            <div className='flex gap-4'>
              <a href='#' aria-label='Instagram' className='hover:opacity-80'>
                <svg className='w-7 h-7' fill='currentColor' viewBox='0 0 24 24'>
                  <path d='M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z'/>
                </svg>
              </a>
              <a href='#' aria-label='TikTok' className='hover:opacity-80'>
                <svg className='w-7 h-7' fill='currentColor' viewBox='0 0 24 24'>
                  <path d='M19.59 6.69a4.83 4.83 0 0 1-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 0 1-5.2 1.74 2.89 2.89 0 0 1 2.31-4.64 2.93 2.93 0 0 1 .88.13V9.4a6.84 6.84 0 0 0-1-.05A6.33 6.33 0 0 0 5 20.1a6.34 6.34 0 0 0 10.86-4.43v-7a8.16 8.16 0 0 0 4.77 1.52v-3.4a4.85 4.85 0 0 1-1-.1z'/>
                </svg>
              </a>
              <a href='#' aria-label='Facebook' className='hover:opacity-80'>
                <svg className='w-7 h-7' fill='currentColor' viewBox='0 0 24 24'>
                  <path d='M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z'/>
                </svg>
              </a>
              <a href='#' aria-label='LinkedIn' className='hover:opacity-80'>
                <svg className='w-7 h-7' fill='currentColor' viewBox='0 0 24 24'>
                  <path d='M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z'/>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Desktop Layout */}
      <div className='hidden md:block'>
        <div className='grid grid-cols-4 gap-16 pb-12 border-b border-[#3a4bb3]'>
          {/* Terminal 1 */}
          <div>
            <div className='text-sm font-bold text-gray-300 mb-6 tracking-wide'>
              STAY AT KLIA TERMINAL 1
            </div>
            <ul className='space-y-4'>
              <li>
                <a href='#' className='text-lg font-semibold hover:underline'>
                  Sleep Lounge
                </a>
              </li>
            </ul>
          </div>

          {/* Terminal 2 */}
          <div>
            <div className='text-sm font-bold text-gray-300 mb-6 tracking-wide'>
              STAY AT KLIA TERMINAL 2
            </div>
            <ul className='space-y-4'>
              <li>
                <a href='#' className='text-lg font-semibold hover:underline'>
                  MAX
                </a>
              </li>
              <li>
                <a href='#' className='text-lg font-semibold hover:underline'>
                  Landside
                </a>
              </li>
              <li>
                <a href='#' className='text-lg font-semibold hover:underline'>
                  Airside
                </a>
              </li>
            </ul>
          </div>

          {/* Discover */}
          <div>
            <div className='text-sm font-bold text-gray-300 mb-6 tracking-wide'>
              DISCOVER
            </div>
            <ul className='space-y-4'>
              <li>
                <a href='#' className='text-lg hover:underline'>
                  About Us
                </a>
              </li>
              <li>
                <a href='#' className='text-lg hover:underline'>
                  Capsule Highlight
                </a>
              </li>
              <li>
                <a href='#' className='text-lg hover:underline'>
                  Our Commitment
                </a>
              </li>
              <li>
                <a href='#' className='text-lg hover:underline'>
                  Newsroom
                </a>
              </li>
              <li>
                <a href='#' className='text-lg hover:underline'>
                  FAQ
                </a>
              </li>
              <li>
                <a href='#' className='text-lg hover:underline'>
                  CSR
                </a>
              </li>
            </ul>
          </div>

          {/* Reach Us */}
          <div>
            <div className='text-sm font-bold text-gray-300 mb-6 tracking-wide'>
              REACH US
            </div>
            <ul className='space-y-4 mb-8'>
              <li>
                <a href='#' className='text-lg hover:underline'>
                  Contact
                </a>
              </li>
              <li>
                <a href='#' className='text-lg hover:underline'>
                  Career
                </a>
              </li>
            </ul>
            {/* Social Icons */}
            <div className='flex gap-4'>
              <a href='#' aria-label='Instagram' className='hover:opacity-80'>
                <svg className='w-7 h-7' fill='currentColor' viewBox='0 0 24 24'>
                  <path d='M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z'/>
                </svg>
              </a>
              <a href='#' aria-label='TikTok' className='hover:opacity-80'>
                <svg className='w-7 h-7' fill='currentColor' viewBox='0 0 24 24'>
                  <path d='M19.59 6.69a4.83 4.83 0 0 1-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 0 1-5.2 1.74 2.89 2.89 0 0 1 2.31-4.64 2.93 2.93 0 0 1 .88.13V9.4a6.84 6.84 0 0 0-1-.05A6.33 6.33 0 0 0 5 20.1a6.34 6.34 0 0 0 10.86-4.43v-7a8.16 8.16 0 0 0 4.77 1.52v-3.4a4.85 4.85 0 0 1-1-.1z'/>
                </svg>
              </a>
              <a href='#' aria-label='Facebook' className='hover:opacity-80'>
                <svg className='w-7 h-7' fill='currentColor' viewBox='0 0 24 24'>
                  <path d='M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z'/>
                </svg>
              </a>
              <a href='#' aria-label='LinkedIn' className='hover:opacity-80'>
                <svg className='w-7 h-7' fill='currentColor' viewBox='0 0 24 24'>
                  <path d='M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z'/>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>

    {/* Bottom Bar */}
    <div className='max-w-7xl mx-auto pt-8 pb-2'>
      {/* Mobile Bottom Layout */}
      <div className='block md:hidden'>
        {/* Logo and Branding */}
        <div className='flex flex-col items-center gap-6 mb-8'>
          <div className='flex items-center gap-4'>
            <div className='flex items-center gap-2'>
              <svg className='w-8 h-8' fill='none' stroke='currentColor' strokeWidth='2' viewBox='0 0 32 32'>
                <path d='M16 2l13 7v14l-13 7-13-7V9z' />
                <path d='M16 2v28' />
                <path d='M3 9l13 7 13-7' />
              </svg>
              <span className='font-bold text-lg'>CapsuleTransit</span>
            </div>
            <div className='font-bold text-2xl tracking-widest'>MAX</div>
          </div>
        </div>

        {/* Location */}
        <div className='flex items-center justify-center gap-2 text-sm text-white/90 mb-6'>
          <svg className='w-5 h-5' fill='none' stroke='currentColor' strokeWidth='2' viewBox='0 0 24 24'>
            <path d='M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7z' />
            <circle cx='12' cy='9' r='2.5' />
          </svg>
          <span className='text-center'>
            Kuala Lumpur International Airport<br />(KLIA), Malaysia
          </span>
        </div>

        {/* Copyright */}
        <div className='text-xs text-white/70 text-center'>
          © 2025 40FT Container Sdn Bhd<br />
          All rights reserved / <a href='#' className='underline'>Terms & Privacy</a>
        </div>
      </div>

      {/* Desktop Bottom Layout */}
      <div className='hidden md:flex items-center justify-between'>
        {/* Location */}
        <div className='flex items-center gap-2 text-sm text-white/90'>
          <svg className='w-5 h-5' fill='none' stroke='currentColor' strokeWidth='2' viewBox='0 0 24 24'>
            <path d='M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7z' />
            <circle cx='12' cy='9' r='2.5' />
          </svg>
          <span>Kuala Lumpur International Airport (KLIA), Malaysia</span>
        </div>

        {/* Logo and Branding */}
        <div className='flex items-center gap-8'>
          <div className='flex items-center gap-2'>
            <svg className='w-8 h-8' fill='none' stroke='currentColor' strokeWidth='2' viewBox='0 0 32 32'>
              <path d='M16 2l13 7v14l-13 7-13-7V9z' />
              <path d='M16 2v28' />
              <path d='M3 9l13 7 13-7' />
            </svg>
            <span className='font-bold text-lg'>CapsuleTransit</span>
          </div>
          <div className='font-bold text-2xl tracking-widest'>MAX</div>
        </div>

        {/* Copyright */}
        <div className='text-xs text-white/70 text-right'>
          © 2025 40FT Container Sdn Bhd<br />
          All rights reserved / <a href='#' className='underline'>Terms & Privacy</a>
        </div>
      </div>
    </div>
  </footer>
);

export default Footer;