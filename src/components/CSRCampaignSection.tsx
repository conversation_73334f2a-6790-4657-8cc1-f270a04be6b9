import React, { useState } from 'react';

const csrImages = [
  'https://images.unsplash.com/photo-1593113598332-cd288d649433?auto=format&fit=crop&w=800&q=80',
  'https://images.unsplash.com/photo-1469571486292-0ba58a3f068b?auto=format&fit=crop&w=800&q=80',
  'https://images.unsplash.com/photo-1488521787991-ed7bbaae773c?auto=format&fit=crop&w=800&q=80',
  'https://images.unsplash.com/photo-1559027615-cd4628902d4a?auto=format&fit=crop&w=800&q=80',
];

const CSRCampaignSection = () => {
  const [imgIdx, setImgIdx] = useState(0);
  return (
    <section className='w-full bg-gray-50 py-16 md:py-20 px-4'>
      <div className='max-w-7xl mx-auto'>
        {/* Mobile Layout */}
        <div className='block md:hidden'>
          {/* Header */}
          <div className='text-center mb-8'>
            <div className='text-sm font-bold mb-2 text-gray-600 tracking-wider'>CSR CAMPAIGN</div>
            <h2 className='text-3xl font-bold mb-8 text-gray-900'>
              Our Initiative to Give Back
            </h2>
          </div>

          {/* Image */}
          <div className='mb-8'>
            <img
              src={csrImages[imgIdx]}
              alt={`CSR ${imgIdx + 1}`}
              className='rounded-2xl w-full h-64 object-cover shadow-lg'
            />
            {/* Dots */}
            <div className='flex justify-center gap-2 mt-4'>
              {csrImages.map((_, i) => (
                <button
                  key={i}
                  className={`w-3 h-3 rounded-full transition-colors ${
                    i === imgIdx ? 'bg-[#223a8a]' : 'bg-gray-300'
                  }`}
                  onClick={() => setImgIdx(i)}
                  aria-label={`Go to image ${i + 1}`}
                />
              ))}
            </div>
          </div>

          {/* Content */}
          <div className='text-center'>
            <div className='text-lg mb-4 text-gray-900'>Our idea is simple.</div>
            <div className='text-xl font-bold mb-6 leading-relaxed text-gray-900'>
              <span className='text-[#00bfae]'>RM1</span> is donated to{' '}
              <span className='underline font-bold'>Kechara Soup Kitchen</span>{' '}
              to feed the homeless from each check-in at CapsuleTransit.
            </div>
            <div className='mb-8'>
              <div className='text-[#00bfae] text-sm mb-3 font-medium'>
                And as of today, we have donated
              </div>
              <div className='bg-[#11806a] text-white text-2xl font-bold px-6 py-3 rounded-lg inline-block'>
                RM 115,198
              </div>
            </div>
            <div className='text-gray-700 mb-8 text-base leading-relaxed'>
              Join us on this journey to support others while you simply get rested at KLIA!
            </div>
            <div className='flex flex-col gap-4'>
              <button className='border-2 border-[#223a8a] text-[#223a8a] font-bold px-8 py-3 rounded-lg hover:bg-[#223a8a] hover:text-white transition-colors'>
                BOOK NOW
              </button>
              <a
                href='#'
                className='text-[#223a8a] underline font-medium text-base'
              >
                Read Our CSR
              </a>
            </div>
          </div>
        </div>

        {/* Desktop Layout */}
        <div className='hidden md:flex items-center gap-16 lg:gap-20'>
          {/* Left: Content */}
          <div className='flex-1'>
            <div className='mb-8'>
              <div className='text-sm font-bold mb-2 text-gray-600 tracking-wider'>CSR CAMPAIGN</div>
              <h2 className='text-4xl lg:text-5xl font-bold mb-8 text-gray-900 leading-tight'>
                Our Initiative to Give Back
              </h2>
            </div>
            <div className='mb-8'>
              <div className='text-xl mb-4 text-gray-900'>Our idea is simple.</div>
              <div className='text-2xl lg:text-3xl font-bold mb-6 leading-relaxed text-gray-900'>
                <span className='text-[#00bfae]'>RM1</span> is donated to{' '}
                <span className='underline font-bold'>Kechara Soup Kitchen</span>
                <br />
                to feed the homeless from each check-in at CapsuleTransit.
              </div>
              <div className='mb-6 flex items-center gap-4'>
                <span className='text-[#00bfae] text-base font-medium'>
                  And as of today, we have donated
                </span>
                <span className='bg-[#11806a] text-white text-2xl font-bold px-6 py-3 rounded-lg'>
                  RM 115,198
                </span>
              </div>
              <div className='text-gray-700 mb-8 text-lg leading-relaxed'>
                Join us on this journey to support others while you simply get rested at KLIA!
              </div>
              <div className='flex gap-6'>
                <button className='border-2 border-[#223a8a] text-[#223a8a] font-bold px-8 py-3 rounded-lg hover:bg-[#223a8a] hover:text-white transition-colors'>
                  BOOK NOW
                </button>
                <a
                  href='#'
                  className='text-[#223a8a] underline font-medium flex items-center text-lg'
                >
                  Read Our CSR
                </a>
              </div>
            </div>
          </div>

          {/* Right: Image */}
          <div className='flex-1 flex flex-col items-center'>
            <div className='w-full max-w-lg'>
              <img
                src={csrImages[imgIdx]}
                alt={`CSR ${imgIdx + 1}`}
                className='rounded-2xl shadow-xl w-full h-80 lg:h-96 object-cover'
              />
              {/* Dots */}
              <div className='flex justify-center gap-2 mt-6'>
                {csrImages.map((_, i) => (
                  <button
                    key={i}
                    className={`w-3 h-3 rounded-full transition-colors ${
                      i === imgIdx ? 'bg-[#223a8a]' : 'bg-gray-300'
                    }`}
                    onClick={() => setImgIdx(i)}
                    aria-label={`Go to image ${i + 1}`}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CSRCampaignSection; 